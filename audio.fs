const float Fs = 48000.0;
const int N = 512;

// Paramètres audio
float KICK_FREQ_START_HZ = 0.0;
float KICK_FREQ_END_HZ = 99.0;

float BASS_THRESHOLD = 0.25;
float FLASH_DURATION = 0.10;
float GLOBAL_VOLUME_SCALE = 10.0;
float FLASH_COOLDOWN_MS = 234.75;  // ← Ajout : délai minimum entre 2 flashs

// Paramètres visuels
float BASE_BRIGHTNESS = 0.1;
float BASE_SPEED = 0.1;
float MAX_SPEED = 0.6;
float SPEED_SMOOTH = 1000.0;

float BUBBLE_SIZE_MIN = 0.01;
float BUBBLE_SIZE_MAX = 0.3;

float MOVE_RADIUS = 10.0;

// Contrôles fixes
const int BUBBLE_COUNT = 50;
float sizeOscSpeed = 0.05;

// Fonction hash simple pour aléatoire
float hash(float x) {
    return fract(sin(x) * 43758.5453123);
}

// Bruit lissé 2D
float smoothNoise(float x, float y) {
    float i = floor(x);
    float f = fract(x);
    float j = floor(y);
    float g = fract(y);

    float a = hash(i + j * 57.0);
    float b = hash(i + 1.0 + j * 57.0);
    float c = hash(i + (j + 1.0) * 57.0);
    float d = hash(i + 1.0 + (j + 1.0) * 57.0);

    float u = f * f * (3.0 - 2.0 * f);
    float v = g * g * (3.0 - 2.0 * g);

    return mix(mix(a, b, u), mix(c, d, u), v);
}

int freqToIndex(float freq) {
    return int(clamp(freq * float(N) / Fs, 0.0, float(N - 1)));
}

float getFreqRangeAverage(int startIdx, int endIdx) {
    float sum = 0.0;
    int count = endIdx - startIdx;
    for (int i = startIdx; i < endIdx; i++) {
        sum += iAudio[i];
    }
    return sum / float(count);
}

float getGlobalVolume() {
    float sum = 0.0;
    for (int i = 0; i < 256; i++) {
        sum += iAudio[i];
    }
    return sum / 256.0;
}

// Fonction metaballs qui fusionne les bulles
float metaballs(vec2 uv, vec3 positions[BUBBLE_COUNT]) {
    float sum = 0.0;
    for (int i = 0; i < BUBBLE_COUNT; i++) {
        float r = positions[i].z;
        float d = distance(uv, positions[i].xy);
        sum += (r * r) / (d * d + 0.001); // éviter division par zéro
    }
    return sum;
}

void mainImage(out vec4 fragColor, in vec2 fragCoord) {
    vec2 uv = (fragCoord.xy - 0.5 * iResolution.xy) / iResolution.y;

    int startIdx = freqToIndex(KICK_FREQ_START_HZ);
    int endIdx = freqToIndex(KICK_FREQ_END_HZ);

    float freqAvg = getFreqRangeAverage(startIdx, endIdx);
    float volume = getGlobalVolume();

    float volClamped = clamp(volume * GLOBAL_VOLUME_SCALE, 0.0, 1.0);
    float speedFactor = mix(BASE_SPEED, MAX_SPEED, smoothstep(0.0, SPEED_SMOOTH, volClamped));

    float intensity = max(volClamped, BASE_BRIGHTNESS);

    // Positions + tailles des bulles
    vec3 positions[BUBBLE_COUNT];

    for (int i = 0; i < BUBBLE_COUNT; i++) {
        float seedX = float(i) * 12.9898;
        float seedY = float(i) * 78.233;

        float t = iTime * speedFactor;

        float posX = MOVE_RADIUS * (smoothNoise(t + seedX * 10.0, seedX) - 0.5);
        float posY = MOVE_RADIUS * (smoothNoise(t * 1.3 + 10.0 + seedY * 10.0, seedY) - 0.5);

        float sizeBase = mix(BUBBLE_SIZE_MIN, BUBBLE_SIZE_MAX, hash(seedX + seedY));
        float sizeOsc = 0.1 * sin(iTime * sizeOscSpeed + float(i) * 5.0);
        float radius = sizeBase + sizeOsc;

        positions[i] = vec3(posX, posY, radius);
    }

    // Calcul du champ metaball
    float metaballValue = metaballs(uv, positions);

    float threshold = 1.0;
    float mask = smoothstep(threshold, threshold - 0.1, metaballValue);

    // Flash avec cooldown
    bool kick = freqAvg > BASS_THRESHOLD;

    float cooldownSeconds = FLASH_COOLDOWN_MS / 1000.0;
    float flashWindow = 0.05;
    bool flashActive = kick && mod(iTime, cooldownSeconds) < flashWindow;

    float flash = flashActive ? 1.0 : 0.0;

    vec3 baseColor = mix(vec3(1.0, 0.0, 0.0), vec3(1.0), flash);

    vec3 finalColor = baseColor * intensity * mask;

    fragColor = vec4(finalColor, 1.0);
}
