const float Fs = 48000.0;
const int N = 256;  // Taille réelle du tableau iAudio (basé sur getGlobalVolume())

// Paramètres audio - Fréquences réelles des kicks de batterie
// Avec N=256 et Fs=48kHz, chaque bin = 187.5 Hz
// Bin 0 = 0-187.5Hz, Bin 1 = 187.5-375Hz, etc.
float KICK_FREQ_START_HZ = 60.0;    // Fréquence fondamentale typique des kicks
float KICK_FREQ_END_HZ = 250.0;     // Inclut les harmoniques importantes

float BASS_THRESHOLD = 0.15;  // Seuil ajusté pour la détection des kicks dans la plage 20-120 Hz
float FLASH_DURATION = 0.10;
float GLOBAL_VOLUME_SCALE = 10.0;
float FLASH_COOLDOWN_MS = 234.75;  // ← Ajout : délai minimum entre 2 flashs

// couleur du flash
float FLASH_COLOR_R = 0.720;
float FLASH_COLOR_G = 0.720;
float FLASH_COLOR_B = 0.720;

// === PARAMÈTRES D'ANIMATIONS DE FLASH ===
float ANIMATION_CHANGE_INTERVAL = 120.0;  // ⚙️ TEMPS D'ATTENTE AVANT CHANGEMENT D'ANIMATION (en secondes)
bool FLASH_OVER_BUBBLES = true;         // ⚙️ AFFICHER LES FLASHS PAR-DESSUS LES BULLES (true) OU EN DESSOUS (false)

// Animation 1: Flash sur les bords
float BORDER_FLASH_WIDTH = 0.15;         // Largeur de la zone de flash sur les bords
float BORDER_FLASH_SOFTNESS = 0.05;      // Douceur de la transition

// Animation 2: Flash central
float CENTER_FLASH_RADIUS = 0.3;         // Rayon du flash central
float CENTER_FLASH_SOFTNESS = 0.1;       // Douceur de la transition

// Animation 3: Onde depuis le centre (déclenchée par kick)
float WAVE_FROM_CENTER_SPEED = 2.0;      // Vitesse de propagation de l'onde
float WAVE_FROM_CENTER_WIDTH = 1.0;     // Largeur de l'onde
float WAVE_FROM_CENTER_DURATION = 1.5;   // Durée de vie d'une onde (en secondes)

// Animation 4: Onde vers le centre (déclenchée par kick)
float WAVE_TO_CENTER_SPEED = 1.5;        // Vitesse de convergence vers le centre
float WAVE_TO_CENTER_WIDTH = 1.0;        // Largeur de l'onde
float WAVE_TO_CENTER_DURATION = 2.0;     // Durée de vie d'une onde (en secondes)

// Animation 5: Rayons vers le centre
float RAYS_TO_CENTER_COUNT = 8.0;        // Nombre de rayons
float RAYS_TO_CENTER_WIDTH = 0.2;       // Largeur des rayons
float RAYS_TO_CENTER_SPEED = 1.8;        // Vitesse de propagation

// Animation 6: Flash en croix
float CROSS_FLASH_WIDTH = 0.08;          // Largeur des branches de la croix
float CROSS_FLASH_SOFTNESS = 0.03;       // Douceur de la transition

// Animation 7: Flash en spirale
float SPIRAL_FLASH_ARMS = 3.0;           // Nombre de bras de la spirale
float SPIRAL_FLASH_SPEED = 1.0;          // Vitesse de rotation
float SPIRAL_FLASH_WIDTH = 0.05;         // Largeur des bras

// Animation 8: Flash en vagues horizontales
float HORIZONTAL_WAVES_COUNT = 5.0;      // Nombre de vagues
float HORIZONTAL_WAVES_SPEED = 2.5;      // Vitesse de déplacement

// Animation 9: Flash en cercles concentriques
float CONCENTRIC_CIRCLES_COUNT = 4.0;    // Nombre de cercles
float CONCENTRIC_CIRCLES_SPEED = 1.2;    // Vitesse d'expansion
float CONCENTRIC_CIRCLES_WIDTH = 1.0;   // Largeur des cercles

// Animation 10: Flash en damier
float CHECKERBOARD_SIZE = 8.0;           // Taille des cases du damier
float CHECKERBOARD_SPEED = 3.0;          // Vitesse d'animation

// === NOUVELLES ANIMATIONS (11-20) ===

// Animation 11: Flash en diagonales
float DIAGONAL_FLASH_COUNT = 6.0;        // Nombre de diagonales
float DIAGONAL_FLASH_WIDTH = 0.05;       // Largeur des diagonales
float DIAGONAL_FLASH_SPEED = 2.0;        // Vitesse de déplacement

// Animation 12: Flash en explosion d'étoile
float STAR_EXPLOSION_RAYS = 12.0;        // Nombre de rayons de l'étoile
float STAR_EXPLOSION_SPEED = 3.0;        // Vitesse d'expansion
float STAR_EXPLOSION_WIDTH = 0.3;       // Largeur des rayons

// Animation 13: Flash en vortex
float VORTEX_FLASH_ARMS = 4.0;           // Nombre de bras du vortex
float VORTEX_FLASH_SPEED = 2.5;          // Vitesse de rotation
float VORTEX_FLASH_TIGHTNESS = 0.5;      // Resserrement du vortex

// Animation 14: Flash en zigzag
float ZIGZAG_FLASH_FREQUENCY = 8.0;      // Fréquence du zigzag
float ZIGZAG_FLASH_AMPLITUDE = 0.3;      // Amplitude du zigzag
float ZIGZAG_FLASH_SPEED = 2.0;          // Vitesse de déplacement

// Animation 15: Flash en hexagones
float HEXAGON_FLASH_SIZE = 0.2;          // Taille des hexagones
float HEXAGON_FLASH_SPACING = 0.4;       // Espacement entre hexagones
float HEXAGON_FLASH_ROTATION = 1.0;      // Vitesse de rotation

// Animation 16: Flash en tunnel
float TUNNEL_FLASH_RINGS = 8.0;          // Nombre d'anneaux du tunnel
float TUNNEL_FLASH_SPEED = 2.0;          // Vitesse de déplacement
float TUNNEL_FLASH_WIDTH = 0.05;         // Largeur des anneaux

// Animation 17: Flash en plasma
float PLASMA_FLASH_COMPLEXITY = 3.0;     // Complexité du plasma
float PLASMA_FLASH_SPEED = 1.5;          // Vitesse d'animation
float PLASMA_FLASH_SCALE = 4.0;          // Échelle du motif

// Animation 18: Flash en losanges
float DIAMOND_FLASH_SIZE = 0.15;         // Taille des losanges
float DIAMOND_FLASH_SPACING = 0.3;       // Espacement entre losanges
float DIAMOND_FLASH_ROTATION = 1.2;      // Vitesse de rotation

// Animation 19: Flash en ondulations radiales
float RADIAL_RIPPLE_COUNT = 6.0;         // Nombre d'ondulations
float RADIAL_RIPPLE_SPEED = 2.5;         // Vitesse de propagation
float RADIAL_RIPPLE_WIDTH = 0.08;        // Largeur des ondulations

// Animation 20: Flash en fractale
float FRACTAL_FLASH_ITERATIONS = 4.0;    // Nombre d'itérations fractales
float FRACTAL_FLASH_SCALE = 2.0;         // Échelle de la fractale
float FRACTAL_FLASH_SPEED = 1.8;         // Vitesse d'animation

// Paramètres visuels
float BASE_BRIGHTNESS = 0.1;
float BASE_SPEED = 0.1;
float MAX_SPEED = 0.6;
float SPEED_SMOOTH = 1000.0;

float BUBBLE_SIZE_MIN = 0.01;
float BUBBLE_SIZE_MAX = 0.3;

float MOVE_RADIUS = 10.0;

// Contrôles fixes
const int BUBBLE_COUNT = 50;
float sizeOscSpeed = 0.05;

// Fonction hash simple pour aléatoire
float hash(float x) {
    return fract(sin(x) * 43758.5453123);
}

// === SYSTÈME DE GESTION DES ONDES MULTIPLES ===
const int MAX_WAVES = 8;  // Nombre maximum d'ondes simultanées

// Structure pour stocker les informations d'une onde
struct Wave {
    float startTime;
    float isActive;
};

// Fonction pour gérer les ondes déclenchées par kick
float getKickTriggeredWave(vec2 uv, float currentTime, bool kickActive, float waveSpeed, float waveWidth, float waveDuration, bool fromCenter) {
    if (!kickActive) return 0.0;

    float result = 0.0;
    float cooldownSeconds = FLASH_COOLDOWN_MS / 1000.0;

    // Créer plusieurs ondes basées sur les kicks récents
    for (int i = 0; i < MAX_WAVES; i++) {
        float waveOffset = float(i) * cooldownSeconds * 0.5;
        float waveStartTime = floor((currentTime - waveOffset) / cooldownSeconds) * cooldownSeconds + waveOffset;
        float waveAge = currentTime - waveStartTime;

        // Vérifier si cette onde est active
        if (waveAge >= 0.0 && waveAge <= waveDuration) {
            float dist = length(uv);
            float wavePos;

            if (fromCenter) {
                wavePos = waveAge * waveSpeed;
            } else {
                wavePos = 1.5 - waveAge * waveSpeed;
            }

            float waveDist = abs(dist - wavePos);
            float waveIntensity = 1.0 - smoothstep(0.0, waveWidth, waveDist);

            // Fade out progressif
            float fadeOut = 1.0 - smoothstep(waveDuration * 0.6, waveDuration, waveAge);
            waveIntensity *= fadeOut;

            result = max(result, waveIntensity);
        }
    }

    return result;
}

// === FONCTIONS D'ANIMATIONS DE FLASH ===

// Animation 1: Flash sur les bords
float borderFlash(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float distToBorder = min(min(abs(uv.x), abs(uv.y)),
                            min(abs(1.0 - abs(uv.x)), abs(1.0 - abs(uv.y))));
    float aspect = iResolution.x / iResolution.y;
    distToBorder = min(distToBorder, min(abs(uv.x * aspect), abs(uv.y)));

    return smoothstep(BORDER_FLASH_WIDTH, BORDER_FLASH_WIDTH - BORDER_FLASH_SOFTNESS, distToBorder);
}

// Animation 2: Flash central
float centerFlash(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float dist = length(uv);
    return smoothstep(CENTER_FLASH_RADIUS, CENTER_FLASH_RADIUS - CENTER_FLASH_SOFTNESS, dist);
}

// Animation 3: Onde depuis le centre (déclenchée par kick)
float waveFromCenter(vec2 uv, float time, bool active) {
    return getKickTriggeredWave(uv, time, active, WAVE_FROM_CENTER_SPEED, WAVE_FROM_CENTER_WIDTH, WAVE_FROM_CENTER_DURATION, true);
}

// Animation 4: Onde vers le centre (déclenchée par kick)
float waveToCenter(vec2 uv, float time, bool active) {
    return getKickTriggeredWave(uv, time, active, WAVE_TO_CENTER_SPEED, WAVE_TO_CENTER_WIDTH, WAVE_TO_CENTER_DURATION, false);
}

// Animation 5: Rayons vers le centre
float raysToCenter(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float angle = atan(uv.y, uv.x);
    float rayIndex = floor((angle + 3.14159) / (6.28318 / RAYS_TO_CENTER_COUNT));
    float rayAngle = rayIndex * (6.28318 / RAYS_TO_CENTER_COUNT) - 3.14159;

    float distToRay = abs(sin((angle - rayAngle) * RAYS_TO_CENTER_COUNT * 0.5));
    float rayProgress = mod(time * RAYS_TO_CENTER_SPEED, 2.0);
    float dist = length(uv);

    float rayMask = 1.0 - smoothstep(0.0, RAYS_TO_CENTER_WIDTH, distToRay);
    float progressMask = smoothstep(rayProgress - 0.1, rayProgress, 2.0 - dist) *
                        smoothstep(rayProgress + 0.1, rayProgress, 2.0 - dist);

    return rayMask * progressMask;
}

// Animation 6: Flash en croix
float crossFlash(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float horizontal = 1.0 - smoothstep(0.0, CROSS_FLASH_WIDTH, abs(uv.y));
    float vertical = 1.0 - smoothstep(0.0, CROSS_FLASH_WIDTH, abs(uv.x));

    return max(horizontal, vertical);
}

// Animation 7: Flash en spirale
float spiralFlash(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float dist = length(uv);
    float angle = atan(uv.y, uv.x);
    float spiral = angle + dist * SPIRAL_FLASH_ARMS + time * SPIRAL_FLASH_SPEED;

    float spiralPattern = sin(spiral * 3.0) * 0.5 + 0.5;
    return smoothstep(0.5 - SPIRAL_FLASH_WIDTH, 0.5 + SPIRAL_FLASH_WIDTH, spiralPattern);
}

// Animation 8: Flash en vagues horizontales
float horizontalWaves(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float wave = sin(uv.y * HORIZONTAL_WAVES_COUNT * 6.28318 + time * HORIZONTAL_WAVES_SPEED);
    return smoothstep(-0.3, 0.3, wave);
}

// Animation 9: Flash en cercles concentriques
float concentricCircles(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float dist = length(uv);
    float circles = sin(dist * CONCENTRIC_CIRCLES_COUNT * 6.28318 - time * CONCENTRIC_CIRCLES_SPEED);

    return 1.0 - smoothstep(0.0, CONCENTRIC_CIRCLES_WIDTH, abs(circles));
}

// Animation 10: Flash en damier
float checkerboardFlash(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    vec2 grid = floor(uv * CHECKERBOARD_SIZE + time * CHECKERBOARD_SPEED);
    float checker = mod(grid.x + grid.y, 2.0);

    return checker;
}

// === NOUVELLES ANIMATIONS (11-20) ===

// Animation 11: Flash en diagonales
float diagonalFlash(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float diagonal1 = abs(uv.x + uv.y + sin(time * DIAGONAL_FLASH_SPEED) * 0.5);
    float diagonal2 = abs(uv.x - uv.y + cos(time * DIAGONAL_FLASH_SPEED) * 0.5);

    float pattern1 = 1.0 - smoothstep(0.0, DIAGONAL_FLASH_WIDTH, mod(diagonal1 * DIAGONAL_FLASH_COUNT, 1.0));
    float pattern2 = 1.0 - smoothstep(0.0, DIAGONAL_FLASH_WIDTH, mod(diagonal2 * DIAGONAL_FLASH_COUNT, 1.0));

    return max(pattern1, pattern2);
}

// Animation 12: Flash en explosion d'étoile
float starExplosion(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float dist = length(uv);
    float angle = atan(uv.y, uv.x);

    float rayPattern = sin(angle * STAR_EXPLOSION_RAYS) * 0.5 + 0.5;
    float explosion = sin(dist * 10.0 - time * STAR_EXPLOSION_SPEED) * 0.5 + 0.5;

    return rayPattern * explosion * (1.0 - smoothstep(0.0, STAR_EXPLOSION_WIDTH, abs(rayPattern - 0.5)));
}

// Animation 13: Flash en vortex
float vortexFlash(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float dist = length(uv);
    float angle = atan(uv.y, uv.x);

    float vortex = angle + dist * VORTEX_FLASH_TIGHTNESS + time * VORTEX_FLASH_SPEED;
    float pattern = sin(vortex * VORTEX_FLASH_ARMS) * 0.5 + 0.5;

    return smoothstep(0.3, 0.7, pattern) * (1.0 - smoothstep(0.5, 1.5, dist));
}

// Animation 14: Flash en zigzag
float zigzagFlash(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float zigzag = uv.y + sin(uv.x * ZIGZAG_FLASH_FREQUENCY + time * ZIGZAG_FLASH_SPEED) * ZIGZAG_FLASH_AMPLITUDE;

    return 1.0 - smoothstep(0.0, 0.1, abs(zigzag));
}

// Animation 15: Flash en hexagones
float hexagonFlash(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    // Rotation
    float angle = time * HEXAGON_FLASH_ROTATION;
    vec2 rotUV = vec2(
        uv.x * cos(angle) - uv.y * sin(angle),
        uv.x * sin(angle) + uv.y * cos(angle)
    );

    // Grille hexagonale approximée
    vec2 grid = rotUV / HEXAGON_FLASH_SPACING;
    vec2 hexCoord = floor(grid);
    vec2 hexOffset = fract(grid) - 0.5;

    float hexDist = max(abs(hexOffset.x), abs(hexOffset.y));

    return 1.0 - smoothstep(HEXAGON_FLASH_SIZE - 0.05, HEXAGON_FLASH_SIZE, hexDist);
}

// Animation 16: Flash en tunnel
float tunnelFlash(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float dist = length(uv);
    float tunnel = dist * TUNNEL_FLASH_RINGS - time * TUNNEL_FLASH_SPEED;

    return 1.0 - smoothstep(0.0, TUNNEL_FLASH_WIDTH, abs(sin(tunnel)));
}

// Animation 17: Flash en plasma
float plasmaFlash(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float plasma = sin(uv.x * PLASMA_FLASH_SCALE + time * PLASMA_FLASH_SPEED) +
                   sin(uv.y * PLASMA_FLASH_SCALE + time * PLASMA_FLASH_SPEED * 1.3) +
                   sin((uv.x + uv.y) * PLASMA_FLASH_SCALE * 0.5 + time * PLASMA_FLASH_SPEED * 0.7) +
                   sin(length(uv) * PLASMA_FLASH_SCALE + time * PLASMA_FLASH_SPEED * 2.0);

    return smoothstep(-PLASMA_FLASH_COMPLEXITY, PLASMA_FLASH_COMPLEXITY, plasma);
}

// Animation 18: Flash en losanges
float diamondFlash(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    // Rotation
    float angle = time * DIAMOND_FLASH_ROTATION;
    vec2 rotUV = vec2(
        uv.x * cos(angle) - uv.y * sin(angle),
        uv.x * sin(angle) + uv.y * cos(angle)
    );

    vec2 grid = rotUV / DIAMOND_FLASH_SPACING;
    vec2 gridPos = fract(grid) - 0.5;

    float diamond = abs(gridPos.x) + abs(gridPos.y);

    return 1.0 - smoothstep(DIAMOND_FLASH_SIZE - 0.05, DIAMOND_FLASH_SIZE, diamond);
}

// Animation 19: Flash en ondulations radiales
float radialRipple(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float angle = atan(uv.y, uv.x);
    float ripple = sin(angle * RADIAL_RIPPLE_COUNT + time * RADIAL_RIPPLE_SPEED);

    return 1.0 - smoothstep(0.0, RADIAL_RIPPLE_WIDTH, abs(ripple));
}

// Animation 20: Flash en fractale
float fractalFlash(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float fractal = 0.0;
    vec2 pos = uv * FRACTAL_FLASH_SCALE;

    for (float i = 0.0; i < FRACTAL_FLASH_ITERATIONS; i++) {
        fractal += sin(pos.x + time * FRACTAL_FLASH_SPEED) * sin(pos.y + time * FRACTAL_FLASH_SPEED * 1.1);
        pos *= 2.0;
        fractal *= 0.5;
    }

    return smoothstep(-0.5, 0.5, fractal);
}

// Fonction principale pour sélectionner et exécuter l'animation
float getFlashAnimation(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    // Sélection de l'animation basée sur le temps (change selon ANIMATION_CHANGE_INTERVAL)
    int animationIndex = int(mod(floor(time / ANIMATION_CHANGE_INTERVAL), 20.0));

    if (animationIndex == 0) return borderFlash(uv, time, active);
    else if (animationIndex == 1) return centerFlash(uv, time, active);
    else if (animationIndex == 2) return waveFromCenter(uv, time, active);
    else if (animationIndex == 3) return waveToCenter(uv, time, active);
    else if (animationIndex == 4) return raysToCenter(uv, time, active);
    else if (animationIndex == 5) return crossFlash(uv, time, active);
    else if (animationIndex == 6) return spiralFlash(uv, time, active);
    else if (animationIndex == 7) return horizontalWaves(uv, time, active);
    else if (animationIndex == 8) return concentricCircles(uv, time, active);
    else if (animationIndex == 9) return checkerboardFlash(uv, time, active);
    else if (animationIndex == 10) return diagonalFlash(uv, time, active);
    else if (animationIndex == 11) return starExplosion(uv, time, active);
    else if (animationIndex == 12) return vortexFlash(uv, time, active);
    else if (animationIndex == 13) return zigzagFlash(uv, time, active);
    else if (animationIndex == 14) return hexagonFlash(uv, time, active);
    else if (animationIndex == 15) return tunnelFlash(uv, time, active);
    else if (animationIndex == 16) return plasmaFlash(uv, time, active);
    else if (animationIndex == 17) return diamondFlash(uv, time, active);
    else if (animationIndex == 18) return radialRipple(uv, time, active);
    else return fractalFlash(uv, time, active);
}

// Bruit lissé 2D
float smoothNoise(float x, float y) {
    float i = floor(x);
    float f = fract(x);
    float j = floor(y);
    float g = fract(y);

    float a = hash(i + j * 57.0);
    float b = hash(i + 1.0 + j * 57.0);
    float c = hash(i + (j + 1.0) * 57.0);
    float d = hash(i + 1.0 + (j + 1.0) * 57.0);

    float u = f * f * (3.0 - 2.0 * f);
    float v = g * g * (3.0 - 2.0 * g);

    return mix(mix(a, b, u), mix(c, d, u), v);
}

int freqToIndex(float freq) {
    // Pour un spectre FFT de 256 bins, chaque bin représente Fs/N Hz
    // Bin 0 = 0 Hz, Bin 1 = Fs/N Hz, ..., Bin N-1 = (N-1)*Fs/N Hz
    // Formule : index = freq * N / Fs
    float index = freq * float(N) / Fs;
    return int(clamp(index, 0.0, float(N - 1)));
}

float getFreqRangeAverage(int startIdx, int endIdx) {
    float sum = 0.0;
    int count = endIdx - startIdx;
    for (int i = startIdx; i < endIdx; i++) {
        sum += iAudio[i];
    }
    return sum / float(count);
}

float getGlobalVolume() {
    float sum = 0.0;
    for (int i = 0; i < 256; i++) {
        sum += iAudio[i];
    }
    return sum / 256.0;
}

// Fonction metaballs qui fusionne les bulles
float metaballs(vec2 uv, vec3 positions[BUBBLE_COUNT]) {
    float sum = 0.0;
    for (int i = 0; i < BUBBLE_COUNT; i++) {
        float r = positions[i].z;
        float d = distance(uv, positions[i].xy);
        sum += (r * r) / (d * d + 0.001); // éviter division par zéro
    }
    return sum;
}

void mainImage(out vec4 fragColor, in vec2 fragCoord) {
    vec2 uv = (fragCoord.xy - 0.5 * iResolution.xy) / iResolution.y;

    int startIdx = freqToIndex(KICK_FREQ_START_HZ);
    int endIdx = freqToIndex(KICK_FREQ_END_HZ);

    float freqAvg = getFreqRangeAverage(startIdx, endIdx);
    float volume = getGlobalVolume();

    float volClamped = clamp(volume * GLOBAL_VOLUME_SCALE, 0.0, 1.0);
    float speedFactor = mix(BASE_SPEED, MAX_SPEED, smoothstep(0.0, SPEED_SMOOTH, volClamped));

    float intensity = max(volClamped, BASE_BRIGHTNESS);

    // Positions + tailles des bulles
    vec3 positions[BUBBLE_COUNT];

    for (int i = 0; i < BUBBLE_COUNT; i++) {
        float seedX = float(i) * 12.9898;
        float seedY = float(i) * 78.233;

        float t = iTime * speedFactor;

        float posX = MOVE_RADIUS * (smoothNoise(t + seedX * 10.0, seedX) - 0.5);
        float posY = MOVE_RADIUS * (smoothNoise(t * 1.3 + 10.0 + seedY * 10.0, seedY) - 0.5);

        float sizeBase = mix(BUBBLE_SIZE_MIN, BUBBLE_SIZE_MAX, hash(seedX + seedY));
        float sizeOsc = 0.1 * sin(iTime * sizeOscSpeed + float(i) * 5.0);
        float radius = sizeBase + sizeOsc;

        positions[i] = vec3(posX, posY, radius);
    }

    // Calcul du champ metaball
    float metaballValue = metaballs(uv, positions);

    float threshold = 1.0;
    float mask = smoothstep(threshold, threshold - 0.1, metaballValue);

    // Flash avec cooldown
    bool kick = freqAvg > BASS_THRESHOLD;

    float cooldownSeconds = FLASH_COOLDOWN_MS / 1000.0;
    float flashWindow = 0.05;
    bool flashActive = kick && mod(iTime, cooldownSeconds) < flashWindow;

    // Calcul de l'animation de flash
    float flashAnimation = getFlashAnimation(uv, iTime, flashActive);

    // Couleur de base des bulles
    vec3 baseColor = vec3(1.0, 0.0, 0.0);
    vec3 bubbleColor = baseColor * intensity * mask;

    if (FLASH_OVER_BUBBLES) {
        // Flash par-dessus les bulles : afficher d'abord les bulles, puis ajouter le flash blanc
        vec3 flashColor = vec3(FLASH_COLOR_R, FLASH_COLOR_G, FLASH_COLOR_B);
        vec3 finalColor = mix(bubbleColor, flashColor, flashAnimation);
        fragColor = vec4(finalColor, 1.0);
    } else {
        // Flash en dessous des bulles : mélanger la couleur de base avec le flash, puis appliquer le masque
        vec3 flashColor = vec3(FLASH_COLOR_R, FLASH_COLOR_G, FLASH_COLOR_B);
        vec3 colorWithFlash = mix(baseColor, flashColor, flashAnimation);
        vec3 finalColor = colorWithFlash * intensity * mask;
        fragColor = vec4(finalColor, 1.0);
    }
}
