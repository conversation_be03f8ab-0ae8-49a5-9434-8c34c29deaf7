const float Fs = 48000.0;
const int N = 256;  // Taille réelle du tableau iAudio (basé sur getGlobalVolume())

// Paramètres audio - Fréquences réelles des kicks de batterie
// Avec N=256 et Fs=48kHz, chaque bin = 187.5 Hz
// Bin 0 = 0-187.5Hz, Bin 1 = 187.5-375Hz, etc.
float KICK_FREQ_START_HZ = 60.0;    // Fréquence fondamentale typique des kicks
float KICK_FREQ_END_HZ = 250.0;     // Inclut les harmoniques importantes

float BASS_THRESHOLD = 0.15;  // Seuil ajusté pour la détection des kicks dans la plage 20-120 Hz
float FLASH_DURATION = 0.10;
float GLOBAL_VOLUME_SCALE = 10.0;
float FLASH_COOLDOWN_MS = 234.75;  // ← Ajout : délai minimum entre 2 flashs

// === PARAMÈTRES D'ANIMATIONS DE FLASH ===
float ANIMATION_CHANGE_INTERVAL = 10.0;  // Changement d'animation toutes les 10s

// Animation 1: Flash sur les bords
float BORDER_FLASH_WIDTH = 0.15;         // Largeur de la zone de flash sur les bords
float BORDER_FLASH_SOFTNESS = 0.05;      // Douceur de la transition

// Animation 2: Flash central
float CENTER_FLASH_RADIUS = 0.3;         // Rayon du flash central
float CENTER_FLASH_SOFTNESS = 0.1;       // Douceur de la transition

// Animation 3: Onde depuis le centre
float WAVE_FROM_CENTER_SPEED = 2.0;      // Vitesse de propagation de l'onde
float WAVE_FROM_CENTER_WIDTH = 0.08;     // Largeur de l'onde

// Animation 4: Onde vers le centre
float WAVE_TO_CENTER_SPEED = 1.5;        // Vitesse de convergence vers le centre
float WAVE_TO_CENTER_WIDTH = 0.1;        // Largeur de l'onde

// Animation 5: Rayons vers le centre
float RAYS_TO_CENTER_COUNT = 8.0;        // Nombre de rayons
float RAYS_TO_CENTER_WIDTH = 0.02;       // Largeur des rayons
float RAYS_TO_CENTER_SPEED = 1.8;        // Vitesse de propagation

// Animation 6: Flash en croix
float CROSS_FLASH_WIDTH = 0.08;          // Largeur des branches de la croix
float CROSS_FLASH_SOFTNESS = 0.03;       // Douceur de la transition

// Animation 7: Flash en spirale
float SPIRAL_FLASH_ARMS = 3.0;           // Nombre de bras de la spirale
float SPIRAL_FLASH_SPEED = 1.0;          // Vitesse de rotation
float SPIRAL_FLASH_WIDTH = 0.05;         // Largeur des bras

// Animation 8: Flash en vagues horizontales
float HORIZONTAL_WAVES_COUNT = 5.0;      // Nombre de vagues
float HORIZONTAL_WAVES_SPEED = 2.5;      // Vitesse de déplacement

// Animation 9: Flash en cercles concentriques
float CONCENTRIC_CIRCLES_COUNT = 4.0;    // Nombre de cercles
float CONCENTRIC_CIRCLES_SPEED = 1.2;    // Vitesse d'expansion
float CONCENTRIC_CIRCLES_WIDTH = 0.03;   // Largeur des cercles

// Animation 10: Flash en damier
float CHECKERBOARD_SIZE = 8.0;           // Taille des cases du damier
float CHECKERBOARD_SPEED = 3.0;          // Vitesse d'animation

// Paramètres visuels
float BASE_BRIGHTNESS = 0.1;
float BASE_SPEED = 0.1;
float MAX_SPEED = 0.6;
float SPEED_SMOOTH = 1000.0;

float BUBBLE_SIZE_MIN = 0.01;
float BUBBLE_SIZE_MAX = 0.3;

float MOVE_RADIUS = 10.0;

// Contrôles fixes
const int BUBBLE_COUNT = 50;
float sizeOscSpeed = 0.05;

// Fonction hash simple pour aléatoire
float hash(float x) {
    return fract(sin(x) * 43758.5453123);
}

// === FONCTIONS D'ANIMATIONS DE FLASH ===

// Animation 1: Flash sur les bords
float borderFlash(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float distToBorder = min(min(abs(uv.x), abs(uv.y)),
                            min(abs(1.0 - abs(uv.x)), abs(1.0 - abs(uv.y))));
    float aspect = iResolution.x / iResolution.y;
    distToBorder = min(distToBorder, min(abs(uv.x * aspect), abs(uv.y)));

    return smoothstep(BORDER_FLASH_WIDTH, BORDER_FLASH_WIDTH - BORDER_FLASH_SOFTNESS, distToBorder);
}

// Animation 2: Flash central
float centerFlash(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float dist = length(uv);
    return smoothstep(CENTER_FLASH_RADIUS, CENTER_FLASH_RADIUS - CENTER_FLASH_SOFTNESS, dist);
}

// Animation 3: Onde depuis le centre
float waveFromCenter(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float dist = length(uv);
    float wavePos = mod(time * WAVE_FROM_CENTER_SPEED, 2.0);
    float waveDist = abs(dist - wavePos);

    return 1.0 - smoothstep(0.0, WAVE_FROM_CENTER_WIDTH, waveDist);
}

// Animation 4: Onde vers le centre
float waveToCenter(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float dist = length(uv);
    float wavePos = 2.0 - mod(time * WAVE_TO_CENTER_SPEED, 2.0);
    float waveDist = abs(dist - wavePos);

    return 1.0 - smoothstep(0.0, WAVE_TO_CENTER_WIDTH, waveDist);
}

// Animation 5: Rayons vers le centre
float raysToCenter(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float angle = atan(uv.y, uv.x);
    float rayIndex = floor((angle + 3.14159) / (6.28318 / RAYS_TO_CENTER_COUNT));
    float rayAngle = rayIndex * (6.28318 / RAYS_TO_CENTER_COUNT) - 3.14159;

    float distToRay = abs(sin((angle - rayAngle) * RAYS_TO_CENTER_COUNT * 0.5));
    float rayProgress = mod(time * RAYS_TO_CENTER_SPEED, 2.0);
    float dist = length(uv);

    float rayMask = 1.0 - smoothstep(0.0, RAYS_TO_CENTER_WIDTH, distToRay);
    float progressMask = smoothstep(rayProgress - 0.1, rayProgress, 2.0 - dist) *
                        smoothstep(rayProgress + 0.1, rayProgress, 2.0 - dist);

    return rayMask * progressMask;
}

// Animation 6: Flash en croix
float crossFlash(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float horizontal = 1.0 - smoothstep(0.0, CROSS_FLASH_WIDTH, abs(uv.y));
    float vertical = 1.0 - smoothstep(0.0, CROSS_FLASH_WIDTH, abs(uv.x));

    return max(horizontal, vertical);
}

// Animation 7: Flash en spirale
float spiralFlash(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float dist = length(uv);
    float angle = atan(uv.y, uv.x);
    float spiral = angle + dist * SPIRAL_FLASH_ARMS + time * SPIRAL_FLASH_SPEED;

    float spiralPattern = sin(spiral * 3.0) * 0.5 + 0.5;
    return smoothstep(0.5 - SPIRAL_FLASH_WIDTH, 0.5 + SPIRAL_FLASH_WIDTH, spiralPattern);
}

// Animation 8: Flash en vagues horizontales
float horizontalWaves(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float wave = sin(uv.y * HORIZONTAL_WAVES_COUNT * 6.28318 + time * HORIZONTAL_WAVES_SPEED);
    return smoothstep(-0.3, 0.3, wave);
}

// Animation 9: Flash en cercles concentriques
float concentricCircles(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    float dist = length(uv);
    float circles = sin(dist * CONCENTRIC_CIRCLES_COUNT * 6.28318 - time * CONCENTRIC_CIRCLES_SPEED);

    return 1.0 - smoothstep(0.0, CONCENTRIC_CIRCLES_WIDTH, abs(circles));
}

// Animation 10: Flash en damier
float checkerboardFlash(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    vec2 grid = floor(uv * CHECKERBOARD_SIZE + time * CHECKERBOARD_SPEED);
    float checker = mod(grid.x + grid.y, 2.0);

    return checker;
}

// Fonction principale pour sélectionner et exécuter l'animation
float getFlashAnimation(vec2 uv, float time, bool active) {
    if (!active) return 0.0;

    // Sélection de l'animation basée sur le temps (change toutes les 10s)
    int animationIndex = int(mod(floor(time / ANIMATION_CHANGE_INTERVAL), 10.0));

    if (animationIndex == 0) return borderFlash(uv, time, active);
    else if (animationIndex == 1) return centerFlash(uv, time, active);
    else if (animationIndex == 2) return waveFromCenter(uv, time, active);
    else if (animationIndex == 3) return waveToCenter(uv, time, active);
    else if (animationIndex == 4) return raysToCenter(uv, time, active);
    else if (animationIndex == 5) return crossFlash(uv, time, active);
    else if (animationIndex == 6) return spiralFlash(uv, time, active);
    else if (animationIndex == 7) return horizontalWaves(uv, time, active);
    else if (animationIndex == 8) return concentricCircles(uv, time, active);
    else return checkerboardFlash(uv, time, active);
}

// Bruit lissé 2D
float smoothNoise(float x, float y) {
    float i = floor(x);
    float f = fract(x);
    float j = floor(y);
    float g = fract(y);

    float a = hash(i + j * 57.0);
    float b = hash(i + 1.0 + j * 57.0);
    float c = hash(i + (j + 1.0) * 57.0);
    float d = hash(i + 1.0 + (j + 1.0) * 57.0);

    float u = f * f * (3.0 - 2.0 * f);
    float v = g * g * (3.0 - 2.0 * g);

    return mix(mix(a, b, u), mix(c, d, u), v);
}

int freqToIndex(float freq) {
    // Pour un spectre FFT de 256 bins, chaque bin représente Fs/N Hz
    // Bin 0 = 0 Hz, Bin 1 = Fs/N Hz, ..., Bin N-1 = (N-1)*Fs/N Hz
    // Formule : index = freq * N / Fs
    float index = freq * float(N) / Fs;
    return int(clamp(index, 0.0, float(N - 1)));
}

float getFreqRangeAverage(int startIdx, int endIdx) {
    float sum = 0.0;
    int count = endIdx - startIdx;
    for (int i = startIdx; i < endIdx; i++) {
        sum += iAudio[i];
    }
    return sum / float(count);
}

float getGlobalVolume() {
    float sum = 0.0;
    for (int i = 0; i < 256; i++) {
        sum += iAudio[i];
    }
    return sum / 256.0;
}

// Fonction metaballs qui fusionne les bulles
float metaballs(vec2 uv, vec3 positions[BUBBLE_COUNT]) {
    float sum = 0.0;
    for (int i = 0; i < BUBBLE_COUNT; i++) {
        float r = positions[i].z;
        float d = distance(uv, positions[i].xy);
        sum += (r * r) / (d * d + 0.001); // éviter division par zéro
    }
    return sum;
}

void mainImage(out vec4 fragColor, in vec2 fragCoord) {
    vec2 uv = (fragCoord.xy - 0.5 * iResolution.xy) / iResolution.y;

    int startIdx = freqToIndex(KICK_FREQ_START_HZ);
    int endIdx = freqToIndex(KICK_FREQ_END_HZ);

    float freqAvg = getFreqRangeAverage(startIdx, endIdx);
    float volume = getGlobalVolume();

    float volClamped = clamp(volume * GLOBAL_VOLUME_SCALE, 0.0, 1.0);
    float speedFactor = mix(BASE_SPEED, MAX_SPEED, smoothstep(0.0, SPEED_SMOOTH, volClamped));

    float intensity = max(volClamped, BASE_BRIGHTNESS);

    // Positions + tailles des bulles
    vec3 positions[BUBBLE_COUNT];

    for (int i = 0; i < BUBBLE_COUNT; i++) {
        float seedX = float(i) * 12.9898;
        float seedY = float(i) * 78.233;

        float t = iTime * speedFactor;

        float posX = MOVE_RADIUS * (smoothNoise(t + seedX * 10.0, seedX) - 0.5);
        float posY = MOVE_RADIUS * (smoothNoise(t * 1.3 + 10.0 + seedY * 10.0, seedY) - 0.5);

        float sizeBase = mix(BUBBLE_SIZE_MIN, BUBBLE_SIZE_MAX, hash(seedX + seedY));
        float sizeOsc = 0.1 * sin(iTime * sizeOscSpeed + float(i) * 5.0);
        float radius = sizeBase + sizeOsc;

        positions[i] = vec3(posX, posY, radius);
    }

    // Calcul du champ metaball
    float metaballValue = metaballs(uv, positions);

    float threshold = 1.0;
    float mask = smoothstep(threshold, threshold - 0.1, metaballValue);

    // Flash avec cooldown
    bool kick = freqAvg > BASS_THRESHOLD;

    float cooldownSeconds = FLASH_COOLDOWN_MS / 1000.0;
    float flashWindow = 0.05;
    bool flashActive = kick && mod(iTime, cooldownSeconds) < flashWindow;

    float flash = flashActive ? 1.0 : 0.0;

    vec3 baseColor = mix(vec3(1.0, 0.0, 0.0), vec3(1.0), flash);

    vec3 finalColor = baseColor * intensity * mask;

    fragColor = vec4(finalColor, 1.0);
}
