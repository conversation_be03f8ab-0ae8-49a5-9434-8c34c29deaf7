last(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused]) > {$VFS.FS.PUSED.MAX.CRIT:"{#FSNAME}"} + 5 or (last(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused]) > {$VFS.FS.PUSED.MAX.CRIT:"{#FSNAME}"} and last(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused],#2) <= {$VFS.FS.PUSED.MAX.CRIT:"{#FSNAME}"} and (last(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},free])<5G or timeleft(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused],1h,100)<1d)) or (last(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused]) > {$VFS.FS.PUSED.MAX.CRIT:"{#FSNAME}"} and last(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused]) - last(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused],#2) > 3 and avg(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused],20m) >= {$VFS.FS.PUSED.MAX.CRIT:"{#FSNAME}"} and avg(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused],20m) <= {$VFS.FS.PUSED.MAX.CRIT:"{#FSNAME}"} + 5 and (last(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},free])<5G or timeleft(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused],1h,100)<1d))