(
  last(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused]) > {$VFS.FS.PUSED.MAX.CRIT:"{#FSNAME}"} + 5
) or (
  // Dépassement soudain : passage de <MAX.CRIT à >MAX.CRIT en une mesure
  last(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused]) > {$VFS.FS.PUSED.MAX.CRIT:"{#FSNAME}"} and
  last(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused],#2) <= {$VFS.FS.PUSED.MAX.CRIT:"{#FSNAME}"} and
  (last(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},free])<5G or timeleft(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused],1h,100)<1d)
) or (
  // Saut soudain depuis zone stable : après 20 mesures stables entre MAX.CRIT et MAX.CRIT+5, saut > 3%
  last(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused]) > {$VFS.FS.PUSED.MAX.CRIT:"{#FSNAME}"} and
  last(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused]) - last(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused],#2) > 3 and
  // Vérifier que les 20 dernières valeurs (avant la dernière) étaient dans la zone stable
  min(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused],#20:2) >= {$VFS.FS.PUSED.MAX.CRIT:"{#FSNAME}"} and
  max(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused],#20:2) <= {$VFS.FS.PUSED.MAX.CRIT:"{#FSNAME}"} + 5 and
  (last(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},free])<5G or timeleft(/Template Module Linux filesystems by Zabbix agent/vfs.fs.size[{#FSNAME},pused],1h,100)<1d)
)